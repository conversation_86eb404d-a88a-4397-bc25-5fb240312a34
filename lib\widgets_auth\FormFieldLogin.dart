import 'package:flutter/material.dart';

class FormFieldLogin extends StatelessWidget {
  final String? hintText;
  final TextEditingController controller;
  final TextInputType? keyboardType;
  final String? Function(String?)? validator; // ✅ نوع صحيح للـ validator


  const FormFieldLogin({
    super.key,
    this.hintText,
    required this.controller,
    this.keyboardType,
    this.validator,
  });
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      child: TextFormField(
        keyboardType: keyboardType,
        controller: controller,
        validator: validator,  //  إزالة () لأن validator هو مرجع دالة وليس استدعاء
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: TextStyle(fontSize: 20, color: Color(0xff24365e)),
          enabledBorder: outlineInputBorder,
          focusedBorder: outlineInputBorder,
          errorBorder: outlineInputBorder,
        ),
      ),
    );
  }
}
// Border
OutlineInputBorder outlineInputBorder =OutlineInputBorder(
  borderRadius: BorderRadius.circular(20),
  borderSide: BorderSide(color: Color(0xff24365e), width: 2),
);
