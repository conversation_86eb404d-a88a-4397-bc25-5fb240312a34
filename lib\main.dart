import 'package:flutter/material.dart';
import 'package:tulipperfumes/Screens_App/Card_Screen.dart';
import 'Screens_App/OTP_Screen.dart';
import "Screens_App/Splash%20Screen.dart" show SplashScreen;

import 'Screens_App/Home_Screen.dart';
import 'Screens_App/Login_Screen.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {


  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      routes: {
        "splashScreen":(context)=> SplashScreen(),
        "loginScreen":(context)=> LoginScreen(),
        "homeScreen":(context)=> HomeScreen(),
        "otpScreen":(context)=> OtpScreen(),
        "cardScreen":(context)=> CardScreen(),
      },

      home: SplashScreen(),

    );
  }
}
