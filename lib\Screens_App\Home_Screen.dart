
import 'package:flutter/material.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
        title: Text("Tulip perfumes ",

        ),
      centerTitle: true,),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: 1,
          onTap: (index){print(index);},
          items: [
        BottomNavigationBarItem(
            label: "home",
            icon: Icon(Icons.home)),
        BottomNavigationBarItem(
            label: "card",

            icon: Icon(Icons.card_giftcard)),


      ]),
      body: Container(),

    );
  }
}
