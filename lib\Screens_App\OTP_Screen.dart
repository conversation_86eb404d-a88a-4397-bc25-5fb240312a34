import 'package:flutter/material.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:tulipperfumes/widgets_auth/ElevatedButton_widget.dart';

class OtpScreen extends StatefulWidget {
  const OtpScreen({super.key});

  @override
  State<OtpScreen> createState() => _OtpScreenState();
}

class _OtpScreenState extends State<OtpScreen> {
  String currentText = "";
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("Otp"), centerTitle: true,),
      body: Container(
        decoration: BoxDecoration(
          color:   Color(0xfff3f3f3)
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text("enter code otp..", style: TextStyle(fontSize: 18)),
            SizedBox(height: 30),
            PinCodeTextField(
              appContext: context,
              length: 4,

              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              onChanged: (value) {
                currentText = value;
              },
              onCompleted: (value) {},
              keyboardType: TextInputType.phone,
            ),
            SizedBox(height: 300),

            ElevatedButtonWidget(
              onPressed: () {
                if (currentText.length == 4) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text("تم إدخال الكود: $currentText")),
                  );
                  Navigator.of(context).pushNamed("homeScreen");
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text("أدخل 4 أرقام كاملة")),
                  );
                }

              },
              text: "confirm",
            ),
          ],
        ),
      ),
    );
  }
}
