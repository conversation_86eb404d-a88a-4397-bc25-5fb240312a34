import 'package:flutter/material.dart';

 class ElevatedButtonWidget extends StatelessWidget {
  final String? text;
  final Function onPressed;
   const ElevatedButtonWidget({super.key, this.text, required this.onPressed});



  @override
  Widget build(BuildContext context) {
    return  ElevatedButton(
      onPressed: () => onPressed(),
      style: ElevatedButton.styleFrom(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: BorderSide(color: Color(0xff24365e), width: 2),
        ),
        padding: EdgeInsets.symmetric(horizontal: 150, vertical: 18),
        alignment: Alignment.center,
      ),

      child: Text(
        text!,
        style: TextStyle(fontSize: 30, color: Color(0xff24365e)),
      ),
    );
  }
}
