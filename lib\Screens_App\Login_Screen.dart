

import 'package:flutter/material.dart';
import 'package:tulipperfumes/widgets_auth/ElevatedButton_widget.dart';
import '../widgets_auth/FormFieldLogin.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  // ✅ Controllers للحقول
  TextEditingController controllerName = TextEditingController();
  TextEditingController controllerAge = TextEditingController();
  TextEditingController controllerNumberPhone = TextEditingController();

  // ✅ GlobalKey واحد فقط للـ Form بدلاً من 3 منفصلة
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  // ✅ إضافة dispose لتنظيف الـ controllers ومنع تسريب الذاكرة
  @override
  void dispose() {
    controllerName.dispose();
    controllerAge.dispose();
    controllerNumberPhone.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xfff3f3f3),
      appBar: AppBar(title: Text("Login Screen"), centerTitle: true),
      body: Form(  // ✅ إضافة Form widget لتجميع الحقول
        key: formKey,  // ✅ ربط الـ GlobalKey بالـ Form
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
          FormFieldLogin(
            hintText: "Enter full name",
            controller: controllerName,
            keyboardType: TextInputType.text,
            validator: (text) {  // ✅ validation حقيقي للاسم
              if (text == null || text.isEmpty) {
                return 'الرجاء إدخال الاسم';
              }
              if (text.length < 3) {
                return 'الاسم يجب أن يكون أكثر من حرفين';
              }
              return null;  // ✅ null يعني البيانات صحيحة
            },
          ),
          FormFieldLogin(
            hintText: "Enter  age",
            controller: controllerAge,
            keyboardType: TextInputType.number,
            validator: (text) {  // ✅ validation حقيقي للعمر
              if (text == null || text.isEmpty) {
                return 'الرجاء إدخال العمر';
              }
              int? age = int.tryParse(text);
              if (age == null) {
                return 'الرجاء إدخال رقم صحيح';
              }
              if (age < 18 ||  age > 70) {
                return 'العمر يجب أن يكون بين 18 و 70';
              }
              return null;  // ✅ null يعني البيانات صحيحة
            },
          ),
          FormFieldLogin(
            hintText: "Enter full number phone",
            controller: controllerNumberPhone,
            keyboardType: TextInputType.phone,
            validator: (text) {  // ✅ validation حقيقي لرقم الهاتف
              if (text == null || text.isEmpty) {
                return 'الرجاء إدخال رقم الهاتف';
              }
              if (text.length != 10) {
                return 'رقم الهاتف يجب أن يكون 10 أرقام ';
              }
              if(!text.startsWith("059")&&!text.startsWith("056")){

                return "يجب ان يبدأ الرقم ب 059 أو 056 ";
              }
              return null;  // ✅ null يعني البيانات صحيحة
            },
          ),

          SizedBox(height: 115),

          ElevatedButtonWidget(
            text: "LOGIN",
            onPressed: () {
              // ✅ التحقق من صحة جميع البيانات قبل الانتقال
              if (formKey.currentState!.validate()) {
                // ✅ إذا كانت البيانات صحيحة، انتقل للصفحة التالية
                Navigator.of(context).pushNamed("otpScreen");

                // ✅ يمكن إضافة رسالة نجاح
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('تم تسجيل الدخول بنجاح!'),
                    backgroundColor: Colors.green,
                  ),
                );
              } else {
                // ✅ إذا كانت البيانات خاطئة، أظهر رسالة خطأ
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('الرجاء تصحيح الأخطاء أعلاه'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
          ),
          ],
        ),  // ✅ إغلاق Column
      ),  // ✅ إغلاق Form
    );
  }
}
