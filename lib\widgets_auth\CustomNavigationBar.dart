import 'package:flutter/material.dart';

class CustomNavigationBar extends StatefulWidget {
  const CustomNavigationBar({super.key});

  // int currentIndex=1;
  // final Function(int) onTap;
  // const CustomNavigationBar({
  //   super.key,
  //   // required this.currentIndex,
  //   // required this.onTap,
  // });

  @override
  State<CustomNavigationBar> createState() => _CustomNavigationBarState();
}

class _CustomNavigationBarState extends State<CustomNavigationBar> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: 1,
        onTap: (index) {
          setState(() {

          });
          print(index);
          if (index == 0) {
            Navigator.of(context).pushNamed("homeScreen");
          } else if (index == 1) {
            Navigator.of(context).pushNamed("cardScreen");
          } else {
            Navigator.of(context).pushNamed("profileScreen");
          }
        },

        items: [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: "Home"),
          BottomNavigationBarItem(
            icon: Icon(Icons.card_giftcard),
            label: "card",
          ),
          BottomNavigationBarItem(
            label: "profile",
            icon: Icon(Icons.tag_faces),
          ),
        ],
      ),
    );
  }
}
